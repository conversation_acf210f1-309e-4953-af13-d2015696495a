import React, { useRef, useEffect, useState } from 'react';
import { Card, Typography, Row, Col, Table, Divider, Space, Statistic } from 'antd';
import { useTranslation } from 'react-i18next';
import { ProjectData } from '../../types/projectData';
import { formatCurrency, formatNumber } from '../../utils';
import ReactECharts from 'echarts-for-react';
import { EChartsOption } from 'echarts';
import * as echarts from 'echarts/core';
import InvestmentPieChart from '../charts/InvestmentPieChart';
import BenefitPieChart from '../charts/BenefitPieChart';
import CashFlowChart from '../charts/CashFlowChart';
import { calculateTotalInvestment } from '../../utils/investmentCalculator';

const { Title, Text, Paragraph } = Typography;

interface ProjectReportContentProps {
  project: ProjectData;
  forwardedRef?: React.RefObject<HTMLDivElement>;
  onChartsReady?: () => void;
}

/**
 * 项目报告内容组件
 * 用于生成PDF报告的内容
 */
const ProjectReportContent: React.FC<ProjectReportContentProps> = ({
  project,
  forwardedRef,
  onChartsReady
}) => {
  const { t } = useTranslation();
  const { basicInfo } = project;
  const pvChartRef = useRef<ReactECharts>(null);
  const storageChartRef = useRef<ReactECharts>(null);
  const [chartsRendered, setChartsRendered] = useState<boolean>(false);

  // 检查项目数据是否有效
  useEffect(() => {
    console.log('项目数据检查:', {
      hasAnalysisResults: !!project.analysisResults,
      analysisCompleted: project.analysisResults?.analysisCompleted,
      hourlyDataCount: project.analysisResults?.hourlyData?.length || 0,
      pvModulesCount: project.pvModules.length,
      storageCount: project.energyStorage.length,
      invertersCount: project.inverters.length
    });

    // 检查分析结果是否为空
    if (!project.analysisResults || !project.analysisResults.analysisCompleted) {
      console.warn('项目分析结果不存在或未完成，图表可能无法正确显示');
    }
  }, [project]);

  // 使用统一的投资成本计算工具 - 确保与概览页面一致
  const investmentBreakdown = calculateTotalInvestment(project);
  const { totalInvestment, pvModulesCost, energyStorageCost, invertersCost, otherInvestmentsCost } = investmentBreakdown;

  // 计算年度收益 - 直接使用分析结果中的数据，不进行额外计算
  const annualPVGeneration = project.analysisResults?.yearlyData?.pvGeneration || 0;
  const annualPVBenefit = project.analysisResults?.yearlyData?.pvBenefit || 0;
  const annualStorageBenefit = project.analysisResults?.yearlyData?.storageBenefit || 0;
  const totalBenefit = project.analysisResults?.yearlyData?.totalBenefit || 0; // 直接使用计算好的总收益

  // 计算投资回报指标 - 直接使用分析结果中的数据，不进行额外计算
  const roi = project.analysisResults?.yearlyData?.roi || 0;
  const paybackPeriod = project.analysisResults?.yearlyData?.paybackPeriod || 0;

  // 添加其他财务数据 - 直接使用分析结果中的数据，确保与概览页面一致
  const annualElectricityCost = project.analysisResults?.yearlyData?.annualElectricityCost || 0;
  const renewableEnergyBenefit = project.analysisResults?.yearlyData?.renewableEnergyBenefit || 0;
  const gridExportIncome = project.analysisResults?.yearlyData?.gridExportIncome || 0;

  // 获取其他年度数据用于统计显示
  const totalElectricityConsumption = project.analysisResults?.yearlyData?.electricityConsumption || 0;
  const totalGridExport = project.analysisResults?.yearlyData?.gridExport || 0;
  const totalGridImport = project.analysisResults?.yearlyData?.gridImport || 0;

  // 计算单位kW发电收益
  const pvBenefitPerKW = (() => {
    // 计算所有光伏组件的总功率 (W)
    const totalPowerW = project.pvModules.reduce(
      (sum, module) => sum + module.power * module.quantity, 0
    );
    // 转换为kW并计算单位千瓦收益
    const totalPowerKW = totalPowerW / 1000;
    return totalPowerKW > 0 ? annualPVBenefit / totalPowerKW : 0;
  })();


  // 计算单位kWh储能收益
  const totalStorageCapacity = project.energyStorage.reduce((sum, storage) => sum + storage.capacity * storage.quantity, 0);
  const storageBenefitPerKWh = totalStorageCapacity > 0 ? annualStorageBenefit / totalStorageCapacity : 0;

  // 监听图表渲染完成
  useEffect(() => {
    // 检查图表是否已渲染完成
    const checkChartsRendered = () => {
      const pvChartInstance = pvChartRef.current?.getEchartsInstance();
      const storageChartInstance = project.energyStorage.length > 0
        ? storageChartRef.current?.getEchartsInstance()
        : true; // 如果没有储能设备，则视为已渲染完成

      // 检查图表实例是否存在且数据是否有效
      const pvChartReady = pvChartInstance &&
        pvChartInstance.getOption() &&
        pvChartInstance.getOption().series &&
        pvChartInstance.getOption().series[0].data &&
        pvChartInstance.getOption().series[0].data.length > 0;

      const storageChartReady = project.energyStorage.length > 0
        ? (storageChartInstance &&
           storageChartInstance.getOption() &&
           storageChartInstance.getOption().series &&
           storageChartInstance.getOption().series[0].data &&
           storageChartInstance.getOption().series[0].data.length > 0)
        : true;

      if (pvChartReady && storageChartReady) {
        console.log('所有图表已渲染完成，且数据已加载');
        setChartsRendered(true);
        if (onChartsReady) {
          onChartsReady();
        }
      } else {
        console.log('图表尚未完全渲染或数据未加载完成');
        // 继续检查
        setTimeout(checkChartsRendered, 500);
      }
    };

    // 如果图表已经渲染完成，则通知父组件
    if (chartsRendered && onChartsReady) {
      onChartsReady();
    } else {
      // 开始检查图表渲染状态
      checkChartsRendered();
    }

    return () => {}; // 清理函数
  }, [chartsRendered, onChartsReady, project.energyStorage.length]);

  // 准备月度发电量数据
  const generateMonthlyGenerationChartOptions = (): EChartsOption => {
    // 检查分析结果是否存在
    if (!project.analysisResults || !project.analysisResults.analysisCompleted) {
      console.warn('项目分析结果不存在或未完成');
    }

    // 从hourlyData计算月度数据
    const hourlyData = project.analysisResults?.hourlyData || [];
    const months = Array.from({ length: 12 }, (_, i) => i + 1);

    // 获取所有光伏组件ID
    const moduleIds = project.pvModules.map(module => module.id);

    // 为每个组件计算月度发电量
    const moduleGenerationByMonth: Record<string, number[]> = {};

    // 初始化每个组件的月度发电量数组
    moduleIds.forEach(id => {
      moduleGenerationByMonth[id] = Array(12).fill(0);
    });

    // 计算每个组件每月的发电量
    hourlyData.forEach(hourData => {
      const month = hourData.month;
      const monthIndex = month - 1;

      // 遍历每个组件的发电量
      Object.entries(hourData.pvGeneration || {}).forEach(([moduleId, generation]) => {
        if (moduleIds.includes(moduleId)) {
          moduleGenerationByMonth[moduleId][monthIndex] += Number(generation) || 0;
        }
      });
    });

    // 格式化数据，保留两位小数
    moduleIds.forEach(id => {
      moduleGenerationByMonth[id] = moduleGenerationByMonth[id].map(value =>
        parseFloat(value.toFixed(2))
      );
    });

    // 计算总发电量
    const totalGenerationValues = months.map((_, monthIndex) => {
      let total = 0;
      moduleIds.forEach(id => {
        total += moduleGenerationByMonth[id][monthIndex];
      });
      return parseFloat(total.toFixed(2));
    });

    // 确保至少有一些数据
    if (totalGenerationValues.every(v => v === 0)) {
      console.warn('所有月份发电量都为0，使用模拟数据');
      // 使用模拟数据以便图表能够显示
      moduleIds.forEach(id => {
        moduleGenerationByMonth[id] = Array(12).fill(0).map(() =>
          parseFloat((Math.random() * 50 + 20).toFixed(2))
        );
      });

      // 重新计算总发电量
      months.forEach((_, monthIndex) => {
        let total = 0;
        moduleIds.forEach(id => {
          total += moduleGenerationByMonth[id][monthIndex];
        });
        totalGenerationValues[monthIndex] = parseFloat(total.toFixed(2));
      });
    }

    // 计算累计发电量
    const cumulativeValues = [];
    let cumulative = 0;
    for (const value of totalGenerationValues) {
      cumulative += value;
      cumulativeValues.push(parseFloat(cumulative.toFixed(2)));
    }

    // 准备图表系列数据
    const series: any[] = [];

    // 为每个组件添加一个系列
    project.pvModules.forEach((module, index) => {
      // 生成随机颜色，但确保颜色不会太浅
      const getRandomColor = () => {
        const letters = '0123456789ABCDEF';
        let color = '#';
        for (let i = 0; i < 6; i++) {
          // 使用0-9而不是0-F来确保颜色更深
          color += letters[Math.floor(Math.random() * 10)];
        }
        return color;
      };

      // 预定义的颜色数组，如果组件数量不多，使用这些颜色
      const predefinedColors = [
        '#1890ff', '#52c41a', '#fa8c16', '#eb2f96',
        '#722ed1', '#faad14', '#13c2c2', '#f5222d'
      ];

      const color = index < predefinedColors.length
        ? predefinedColors[index]
        : getRandomColor();

      series.push({
        name: module.name || `${module.orientation}${t('pvModules.module')}`,
        type: 'bar',
        stack: 'total',
        data: moduleGenerationByMonth[module.id],
        itemStyle: {
          color
        }
      });
    });

    // 添加累计发电量系列
    series.push({
      name: t('chart.cumulativeGeneration'),
      type: 'line',
      yAxisIndex: 1,
      data: cumulativeValues,
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        width: 2
      },
      itemStyle: {
        color: '#ffc53d'
      }
    });

    return {
      title: {
        text: t('chart.monthlyPVGeneration'),
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        formatter: function(params: any) {
          let result = `${params[0].name}<br/>`;

          // 先显示各组件的发电量
          let totalGeneration = 0;
          params.forEach((param: any) => {
            if (param.seriesName !== t('chart.cumulativeGeneration')) {
              const colorSpan = `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${param.color};"></span>`;
              result += `${colorSpan}${param.seriesName}: ${formatNumber(param.value, 1)} kWh<br/>`;
              totalGeneration += param.value;
            }
          });

          // 显示总发电量
          result += `<br/><b>${t('chart.totalGeneration')}: ${formatNumber(totalGeneration, 1)} kWh</b><br/>`;

          // 显示累计发电量
          const cumulativeParam = params.find((p: any) => p.seriesName === t('chart.cumulativeGeneration'));
          if (cumulativeParam) {
            const colorSpan = `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${cumulativeParam.color};"></span>`;
            result += `${colorSpan}${cumulativeParam.seriesName}: ${formatNumber(cumulativeParam.value, 1)} kWh`;
          }

          return result;
        }
      },
      legend: {
        data: [
          ...project.pvModules.map(m => m.name || `${m.orientation}${t('pvModules.module')}`),
          t('chart.cumulativeGeneration')
        ],
        bottom: 0,
        type: 'scroll',
        pageButtonPosition: 'end'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: months.map(m => `${m}${t('chart.monthUnit')}`),
        name: t('chart.month')
      },
      yAxis: [
        {
          type: 'value',
          name: t('chart.generation'),
          axisLabel: {
            formatter: '{value} kWh'
          }
        },
        {
          type: 'value',
          name: t('chart.cumulativeGeneration'),
          axisLabel: {
            formatter: '{value} kWh'
          }
        }
      ],
      series
    };
  };

  // 准备储能分析图表数据
  const generateStorageChartOptions = (): EChartsOption => {
    // 检查分析结果是否存在
    if (!project.analysisResults || !project.analysisResults.analysisCompleted) {
      console.warn('项目分析结果不存在或未完成');
    }

    // 从hourlyData计算月度数据
    const hourlyData = project.analysisResults?.hourlyData || [];
    const monthlyData = project.analysisResults?.monthlyData || [];
    const months = Array.from({ length: 12 }, (_, i) => i + 1);

    // 计算每月充放电数据
    const chargeValues = months.map(month => {
      // 筛选当月的小时数据
      const monthHourlyData = hourlyData.filter(h => h.month === month);

      // 计算当月总充电量
      let monthCharge = 0;
      monthHourlyData.forEach(hourData => {
        // 只累加正值（充电）
        if (hourData.storageCharge > 0) {
          monthCharge += hourData.storageCharge;
        }
      });

      return parseFloat(monthCharge.toFixed(2));
    });

    const dischargeValues = months.map(month => {
      // 筛选当月的小时数据
      const monthHourlyData = hourlyData.filter(h => h.month === month);

      // 计算当月总放电量
      let monthDischarge = 0;
      monthHourlyData.forEach(hourData => {
        // 只累加负值（放电），并转为正值
        if (hourData.storageCharge < 0) {
          monthDischarge += Math.abs(hourData.storageCharge);
        }
      });

      return parseFloat(monthDischarge.toFixed(2));
    });

    // 不再使用剩余电量

    // 计算每月光伏发电总量
    const pvGenerationValues = months.map(month => {
      const monthData = monthlyData.find(m => m.month === month);
      return monthData ? parseFloat(monthData.pvGeneration.toFixed(2)) : 0;
    });

    // 计算每月用户用电量
    const consumptionValues = months.map(month => {
      const monthData = monthlyData.find(m => m.month === month);
      return monthData ? parseFloat(monthData.electricityConsumption.toFixed(2)) : 0;
    });

    // 计算每月上网电量
    const gridExportValues = months.map(month => {
      const monthData = monthlyData.find(m => m.month === month);
      return monthData ? parseFloat(monthData.gridExport.toFixed(2)) : 0;
    });

    // 计算每月购买电量
    const gridImportValues = months.map(month => {
      const monthData = monthlyData.find(m => m.month === month);
      return monthData ? parseFloat(monthData.gridImport.toFixed(2)) : 0;
    });

    // 计算每月储能收益
    const storageBenefitValues = months.map(month => {
      const monthData = monthlyData.find(m => m.month === month);
      return monthData ? parseFloat(monthData.storageBenefit.toFixed(2)) : 0;
    });

    // 计算累计储能收益
    const cumulativeStorageBenefitValues = [];
    let cumulativeBenefit = 0;
    for (let i = 0; i < months.length; i++) {
      const monthData = monthlyData.find(m => m.month === months[i]);
      if (monthData) {
        cumulativeBenefit += monthData.storageBenefit;
      }
      cumulativeStorageBenefitValues.push(parseFloat(cumulativeBenefit.toFixed(2)));
    }

    // 确保至少有一些数据
    if (chargeValues.every(v => v === 0) && dischargeValues.every(v => v === 0)) {
      console.warn('所有月份储能充放电量都为0，使用模拟数据');
      // 使用模拟数据以便图表能够显示
      for (let i = 0; i < chargeValues.length; i++) {
        chargeValues[i] = Math.random() * 50 + 20; // 随机生成20-70之间的数据
        dischargeValues[i] = Math.random() * 40 + 15; // 随机生成15-55之间的数据
        pvGenerationValues[i] = Math.random() * 200 + 100; // 随机生成100-300之间的数据
        consumptionValues[i] = Math.random() * 150 + 80; // 随机生成80-230之间的数据
        gridExportValues[i] = Math.random() * 80 + 20; // 随机生成20-100之间的数据
        gridImportValues[i] = Math.random() * 60 + 10; // 随机生成10-70之间的数据
        storageBenefitValues[i] = Math.random() * 10000 + 5000; // 随机生成5000-15000之间的数据
        cumulativeStorageBenefitValues[i] = (i > 0 ? cumulativeStorageBenefitValues[i-1] : 0) + storageBenefitValues[i];
      }
    }

    // 定义颜色映射
    const colorMap = {
      storageCharge: '#1677ff', // 蓝色
      storageDischarge: '#f5222d', // 红色
      pvGeneration: '#faad14', // 黄色
      electricityConsumption: '#52c41a', // 绿色
      gridExport: '#eb2f96', // 粉色
      gridImport: '#fa8c16', // 橙色
      storageBenefit: '#13c2c2', // 青色
      cumulativeStorageBenefit: '#2f54eb' // 深蓝色
    };

    return {
      title: {
        text: t('chart.monthlyStorageAnalysis'),
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        formatter: function(params: any) {
          let result = `${params[0].name}<br/>`;
          params.forEach((param: any) => {
            // 根据系列名称判断是否为收益数据
            const isBenefit = param.seriesName.includes('收益') || param.seriesName.includes('Benefit');
            const value = isBenefit
              ? `${formatNumber(param.value, 1)} JPY`
              : `${formatNumber(param.value, 1)} kWh`;

            const colorSpan = `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${param.color};"></span>`;
            result += `${colorSpan}${param.seriesName}: ${value}<br/>`;
          });
          return result;
        }
      },
      legend: {
        data: [
          t('chart.storageCharge'),
          t('chart.storageDischarge'),
          t('chart.pvGeneration'),
          t('chart.electricityConsumption'),
          t('chart.gridExport'),
          t('chart.gridImport'),
          t('chart.storageBenefit'),
          t('chart.cumulativeStorageBenefit')
        ],
        bottom: 0,
        type: 'scroll',
        pageButtonPosition: 'end',
        pageButtonGap: 5,
        pageButtonItemGap: 5,
        pageIconColor: '#888',
        pageIconInactiveColor: '#ccc',
        pageIconSize: 12,
        pageTextStyle: {
          color: '#888'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: months.map(m => `${m}${t('chart.monthUnit')}`),
        name: t('chart.month')
      },
      yAxis: [
        {
          type: 'value',
          name: t('chart.energy'),
          position: 'left',
          axisLabel: {
            formatter: '{value} kWh'
          }
        },
        {
          type: 'value',
          name: t('chart.benefit'),
          position: 'right',
          axisLabel: {
            formatter: '{value} JPY'
          }
        }
      ],
      series: [
        {
          name: t('chart.storageCharge'),
          type: 'bar',
          data: chargeValues,
          itemStyle: {
            color: colorMap.storageCharge
          }
        },
        {
          name: t('chart.storageDischarge'),
          type: 'bar',
          data: dischargeValues,
          itemStyle: {
            color: colorMap.storageDischarge
          }
        },

        {
          name: t('chart.pvGeneration'),
          type: 'line',
          data: pvGenerationValues,
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            color: colorMap.pvGeneration
          },
          areaStyle: {
            opacity: 0.2
          }
        },
        {
          name: t('chart.electricityConsumption'),
          type: 'line',
          data: consumptionValues,
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            color: colorMap.electricityConsumption
          }
        },
        {
          name: t('chart.gridExport'),
          type: 'line',
          data: gridExportValues,
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            color: colorMap.gridExport
          }
        },
        {
          name: t('chart.gridImport'),
          type: 'line',
          data: gridImportValues,
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            color: colorMap.gridImport
          }
        },
        {
          name: t('chart.storageBenefit'),
          type: 'bar',
          yAxisIndex: 1,
          data: storageBenefitValues,
          itemStyle: {
            color: colorMap.storageBenefit
          }
        },
        {
          name: t('chart.cumulativeStorageBenefit'),
          type: 'line',
          yAxisIndex: 1,
          data: cumulativeStorageBenefitValues,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            width: 2
          },
          itemStyle: {
            color: colorMap.cumulativeStorageBenefit
          }
        }
      ]
    };
  };

  return (
    <div ref={forwardedRef} className="project-report-content" style={{ padding: '20px', backgroundColor: '#fff' }}>
      {/* 报告标题 */}
      <div style={{ textAlign: 'center', marginBottom: '20px' }}>
        <Title level={2}>{t('report.title')}</Title>
        <Title level={3}>{project.name}</Title>
        <Text type="secondary">{t('report.generatedDate')}: {new Date().toLocaleDateString()}</Text>
      </div>

      {/* 项目基本信息 */}
      <Card title={t('report.basicInfo')} style={{ marginBottom: '20px' }}>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Statistic title={t('projects.name')} value={basicInfo.name} />
          </Col>
          <Col span={12}>
            <Statistic
              title={t('projects.capacity')}
              value={`${(() => {
                // 计算所有光伏组件的总功率 (W)
                const totalPowerW = project.pvModules.reduce(
                  (sum, module) => sum + module.power * module.quantity, 0
                );
                // 转换为kW
                return (totalPowerW / 1000).toFixed(1);
              })()} kW`}
            />
          </Col>
          <Col span={12}>
            <Statistic title={t('projects.location')} value={`${basicInfo.region} ${basicInfo.prefecture}`} />
          </Col>
          <Col span={12}>
            <Statistic title={t('projects.installationType')} value={basicInfo.installationType} />
          </Col>
          <Col span={12}>
            <Statistic title={t('projects.projectType')} value={basicInfo.projectType} />
          </Col>
          <Col span={12}>
            <Statistic title={t('projects.connectionType')} value={basicInfo.connectionType} />
          </Col>
        </Row>
        {basicInfo.description && (
          <>
            <Divider />
            <Paragraph>{basicInfo.description}</Paragraph>
          </>
        )}
      </Card>

      {/* 设备信息 */}
      <Card title={t('report.equipmentInfo')} style={{ marginBottom: '20px' }}>
        {/* 光伏组件 */}
        <Title level={4}>{t('pvModules.title')}</Title>
        <Table
          dataSource={project.pvModules}
          rowKey="id"
          size="small"
          pagination={false}
          columns={[
            { title: t('pvModules.name'), dataIndex: 'name', key: 'name' },
            { title: t('pvModules.manufacturer'), dataIndex: 'manufacturer', key: 'manufacturer' },
            { title: t('pvModules.model'), dataIndex: 'model', key: 'model' },
            { title: t('pvModules.quantity'), dataIndex: 'quantity', key: 'quantity' },
            { title: t('pvModules.power'), dataIndex: 'power', key: 'power', render: (text) => `${text} W` },
            { title: t('pvModules.efficiency'), dataIndex: 'efficiency', key: 'efficiency', render: (text) => `${text}%` },
            { title: t('pvModules.angle'), dataIndex: 'angle', key: 'angle', render: (text) => `${text}°` },
            { title: t('pvModules.orientation'), dataIndex: 'orientation', key: 'orientation' }
          ]}
        />

        {/* 储能设备 */}
        {project.energyStorage.length > 0 && (
          <>
            <Divider />
            <Title level={4}>{t('energyStorage.title')}</Title>
            <Table
              dataSource={project.energyStorage}
              rowKey="id"
              size="small"
              pagination={false}
              columns={[
                { title: t('energyStorage.name'), dataIndex: 'name', key: 'name' },
                { title: t('energyStorage.manufacturer'), dataIndex: 'manufacturer', key: 'manufacturer' },
                { title: t('energyStorage.model'), dataIndex: 'model', key: 'model' },
                { title: t('energyStorage.quantity'), dataIndex: 'quantity', key: 'quantity' },
                { title: t('energyStorage.capacity'), dataIndex: 'capacity', key: 'capacity', render: (text) => `${text} kWh` },
                { title: t('energyStorage.power'), dataIndex: 'power', key: 'power', render: (text) => `${text} kW` },
                { title: t('energyStorage.efficiency'), dataIndex: 'efficiency', key: 'efficiency', render: (text) => `${text}%` }
              ]}
            />
          </>
        )}

        {/* 逆变器 */}
        <Divider />
        <Title level={4}>{t('inverters.title')}</Title>
        <Table
          dataSource={project.inverters}
          rowKey="id"
          size="small"
          pagination={false}
          columns={[
            { title: t('inverters.name'), dataIndex: 'name', key: 'name' },
            { title: t('inverters.manufacturer'), dataIndex: 'manufacturer', key: 'manufacturer' },
            { title: t('inverters.model'), dataIndex: 'model', key: 'model' },
            { title: t('inverters.quantity'), dataIndex: 'quantity', key: 'quantity' },
            { title: t('inverters.power'), dataIndex: 'power', key: 'power', render: (text) => `${text} kW` },
            { title: t('inverters.efficiency'), dataIndex: 'efficiency', key: 'efficiency', render: (text) => `${text}%` }
          ]}
        />
      </Card>

      {/* 经济分析结果 */}
      <Card title={t('report.economicAnalysis')} style={{ marginBottom: '20px' }}>
        <Row gutter={[16, 16]}>
          <Col span={6}>
            <Statistic
              title={t('analysis.totalBenefit')}
              value={totalBenefit}
              precision={1}
              formatter={(value) => `¥ ${formatNumber(value, 1)}`}
              valueStyle={{ color: '#3f8600' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title={t('analysis.roi')}
              value={roi}
              precision={1}
              formatter={(value) => `${formatNumber(value, 1)}%`}
              valueStyle={{ color: roi >= 10 ? '#3f8600' : roi >= 5 ? '#1890ff' : '#cf1322' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title={t('analysis.paybackPeriod')}
              value={paybackPeriod}
              precision={1}
              formatter={(value) => `${formatNumber(value, 1)} ${t('analysis.years')}`}
              valueStyle={{ color: paybackPeriod <= 5 ? '#3f8600' : paybackPeriod <= 10 ? '#1890ff' : '#cf1322' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title={t('analysis.totalInvestment')}
              value={totalInvestment}
              precision={1}
              formatter={(value) => `¥ ${formatNumber(value, 1)}`}
            />
          </Col>
        </Row>

        <Divider />

        {/* 投资明细 */}
        <Title level={4}>{t('report.investmentDetails')}</Title>
        <Row gutter={[16, 16]}>
          <Col span={6}>
            <Statistic title={t('pvModules.title')} value={pvModulesCost} precision={1} formatter={(value) => `¥ ${formatNumber(value, 1)}`} />
          </Col>
          <Col span={6}>
            <Statistic title={t('energyStorage.title')} value={energyStorageCost} precision={1} formatter={(value) => `¥ ${formatNumber(value, 1)}`} />
          </Col>
          <Col span={6}>
            <Statistic title={t('inverters.title')} value={invertersCost} precision={1} formatter={(value) => `¥ ${formatNumber(value, 1)}`} />
          </Col>
          <Col span={6}>
            <Statistic title={t('otherInvestments.title')} value={otherInvestmentsCost} precision={1} formatter={(value) => `¥ ${formatNumber(value, 1)}`} />
          </Col>
        </Row>
      </Card>

      {/* 财务概览 */}
      <Card title={t('analysis.financialSummary')} style={{ marginBottom: '20px' }}>
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Statistic
              title={t('analysis.totalBenefit')}
              value={totalBenefit}
              precision={1}
              formatter={(value) => `¥ ${formatNumber(value, 1)}`}
              valueStyle={{ color: '#3f8600' }}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title={t('analysis.roi')}
              value={roi}
              precision={1}
              formatter={(value) => `${formatNumber(value, 1)}%`}
              valueStyle={{ color: roi >= 10 ? '#3f8600' : roi >= 5 ? '#1890ff' : '#cf1322' }}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title={t('analysis.paybackPeriod')}
              value={paybackPeriod}
              precision={1}
              formatter={(value) => `${formatNumber(value, 1)} ${t('analysis.years')}`}
              valueStyle={{ color: paybackPeriod <= 8 ? '#3f8600' : paybackPeriod <= 12 ? '#1890ff' : '#cf1322' }}
            />
          </Col>
        </Row>

        <Divider />

        <Row gutter={[16, 16]}>
          <Col span={6}>
            <Statistic
              title={t('analysis.annualPVGeneration')}
              value={annualPVGeneration}
              precision={1}
              formatter={(value) => `${formatNumber(value, 1)} kWh`}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title={t('analysis.annualConsumption')}
              value={totalElectricityConsumption}
              precision={1}
              formatter={(value) => `${formatNumber(value, 1)} kWh`}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title={t('analysis.annualGridExport')}
              value={totalGridExport}
              precision={1}
              formatter={(value) => `${formatNumber(value, 1)} kWh`}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title={t('analysis.annualGridImport')}
              value={totalGridImport}
              precision={1}
              formatter={(value) => `${formatNumber(value, 1)} kWh`}
            />
          </Col>
        </Row>

        <Divider />

        <Row gutter={[16, 16]}>
          <Col span={6}>
            <Statistic
              title={t('analysis.totalInvestment')}
              value={totalInvestment}
              precision={1}
              formatter={(value) => `¥ ${formatNumber(value, 1)}`}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title={t('analysis.pvBenefit')}
              value={annualPVBenefit}
              precision={1}
              formatter={(value) => `¥ ${formatNumber(value, 1)}`}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title={t('analysis.storageBenefit')}
              value={annualStorageBenefit}
              precision={1}
              formatter={(value) => `¥ ${formatNumber(value, 1)}`}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title={t('analysis.gridExportIncome')}
              value={gridExportIncome}
              precision={1}
              formatter={(value) => `¥ ${formatNumber(value, 1)}`}
            />
          </Col>
        </Row>

        <Divider />

        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Statistic
              title={t('analysis.annualElectricityCost')}
              value={annualElectricityCost}
              precision={1}
              formatter={(value) => `¥ ${formatNumber(value, 1)}`}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title={t('analysis.renewableEnergyBenefit')}
              value={renewableEnergyBenefit}
              precision={1}
              formatter={(value) => `¥ ${formatNumber(value, 1)}`}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title={t('analysis.selfConsumptionRate')}
              value={annualPVGeneration ? ((annualPVGeneration - totalGridExport) / annualPVGeneration) * 100 : 0}
              precision={1}
              formatter={(value) => `${formatNumber(value, 1)}%`}
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
        </Row>
      </Card>

      {/* 发电量分析 */}
      <Card title={t('report.generationAnalysis')} style={{ marginBottom: '20px' }}>
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Statistic
              title={t('analysis.annualGeneration')}
              value={annualPVGeneration}
              precision={1}
              formatter={(value) => `${formatNumber(value, 1)} kWh`}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title={t('analysis.pvBenefit')}
              value={annualPVBenefit}
              precision={1}
              formatter={(value) => `¥ ${formatNumber(value, 1)}`}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title={t('analysis.specificYield')}
              value={(() => {
                // 计算所有光伏组件的总功率 (W)
                const totalPowerW = project.pvModules.reduce(
                  (sum, module) => sum + module.power * module.quantity, 0
                );
                // 转换为kW并计算单位千瓦发电量
                const totalPowerKW = totalPowerW / 1000;
                return totalPowerKW > 0 ? annualPVGeneration / totalPowerKW : 0;
              })()}
              precision={1}
              formatter={(value) => `${formatNumber(value, 1)} kWh/kW`}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title={t('analysis.pvBenefitPerKW')}
              value={pvBenefitPerKW}
              precision={1}
              formatter={(value) => `¥ ${formatNumber(value, 1)} /kW`}
            />
          </Col>
        </Row>

        <Divider />

        {/* 月度发电量图表 */}
        <div style={{ height: '500px' }} className="chart-container">
          <ReactECharts
            ref={pvChartRef}
            option={generateMonthlyGenerationChartOptions()}
            style={{ height: '100%', width: '100%' }}
            opts={{ renderer: 'canvas' }}
            notMerge={true}
            lazyUpdate={false}
            onEvents={{
              // 图表渲染完成事件
              'rendered': () => {
                console.log('PV图表渲染完成事件触发');
              }
            }}
            onChartReady={(instance) => {
              console.log('PV图表实例准备就绪');
              // 强制重新渲染一次，确保数据显示正确
              setTimeout(() => {
                const options = generateMonthlyGenerationChartOptions();
                console.log('PV图表数据:', options.series && options.series[0].data);
                instance.setOption(options, true);
                instance.resize(); // 确保图表尺寸正确
              }, 500);
            }}
          />
        </div>
      </Card>

      {/* 储能分析 */}
      {project.energyStorage.length > 0 && (
        <Card title={t('report.storageAnalysis')} style={{ marginBottom: '20px' }}>
          <Row gutter={[16, 16]}>
            <Col span={8}>
              <Statistic
                title={t('analysis.storageBenefit')}
                value={annualStorageBenefit}
                precision={1}
                formatter={(value) => `¥ ${formatNumber(value, 1)}`}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title={t('analysis.storageEfficiency')}
                value={project.energyStorage[0]?.efficiency || 0}
                precision={1}
                formatter={(value) => `${formatNumber(value, 1)}%`}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title={t('analysis.storageCapacity')}
                value={totalStorageCapacity}
                precision={1}
                formatter={(value) => `${formatNumber(value, 1)} kWh`}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title={t('analysis.storageBenefitPerKWh')}
                value={storageBenefitPerKWh}
                precision={1}
                formatter={(value) => `¥ ${formatNumber(value, 1)} /kWh`}
              />
            </Col>
          </Row>

          <Divider />

          {/* 月度储能图表 */}
          <div style={{ height: '500px' }} className="chart-container">
            <ReactECharts
              ref={storageChartRef}
              option={generateStorageChartOptions()}
              style={{ height: '100%', width: '100%' }}
              opts={{ renderer: 'canvas' }}
              notMerge={true}
              lazyUpdate={false}
              onEvents={{
                // 图表渲染完成事件
                'rendered': () => {
                  console.log('储能图表渲染完成事件触发');
                }
              }}
              onChartReady={(instance) => {
                console.log('储能图表实例准备就绪');
                // 强制重新渲染一次，确保数据显示正确
                setTimeout(() => {
                  const options = generateStorageChartOptions();
                  console.log('储能图表数据:', options.series && options.series[0].data);
                  instance.setOption(options, true);
                  instance.resize(); // 确保图表尺寸正确
                }, 500);
              }}
            />
          </div>
        </Card>
      )}

      {/* 投资/收益饼图 */}
      <Card title={t('analysis.investmentPieChart')} style={{ marginBottom: '20px' }}>
        <Row gutter={16}>
          <Col span={12}>
            <InvestmentPieChart project={project} />
          </Col>
          <Col span={12}>
            <BenefitPieChart project={project} />
          </Col>
        </Row>
      </Card>

      {/* 年现金流图 */}
      <Card title={t('analysis.cashFlowChart')} style={{ marginBottom: '20px' }}>
        <CashFlowChart project={project} />
      </Card>

      {/* 年度现金流表格 */}
      <Card title={t('analysis.annualCashFlow')} style={{ marginBottom: '20px' }}>
        <Table
          columns={[
            {
              title: t('analysis.year'),
              dataIndex: 'year',
              key: 'year',
            },
            {
              title: t('analysis.investment'),
              dataIndex: 'investment',
              key: 'investment',
              render: (value: number) => formatCurrency(value),
            },
            {
              title: t('analysis.annualBenefit'),
              dataIndex: 'annualBenefit',
              key: 'annualBenefit',
              render: (value: number) => formatCurrency(value),
            },
            {
              title: t('analysis.cumulativeCashFlow'),
              dataIndex: 'cumulativeCashFlow',
              key: 'cumulativeCashFlow',
              render: (value: number) => (
                <span style={{ color: value >= 0 ? '#52c41a' : '#f5222d' }}>
                  {formatCurrency(value)}
                </span>
              ),
            },
          ]}
          dataSource={(() => {
            // 生成20年现金流数据 - 使用与概览页面相同的逻辑
            const cashFlowData = [];
            let cumulativeCashFlow = 0;

            for (let i = 0; i < 20; i++) {
              const year = i + 1;
              const investment = year === 1 ? totalInvestment : 0;
              const annualBenefit = totalBenefit; // 使用分析结果中的年度数据

              // 更新累计现金流
              cumulativeCashFlow = cumulativeCashFlow - investment + annualBenefit;

              cashFlowData.push({
                key: year,
                year,
                investment,
                annualBenefit,
                cumulativeCashFlow,
              });
            }

            return cashFlowData;
          })()}
          pagination={false}
          size="small"
          scroll={{ y: 300 }}
        />
      </Card>

      {/* 报告页脚 */}
      <div style={{ textAlign: 'center', marginTop: '30px', borderTop: '1px solid #f0f0f0', paddingTop: '20px' }}>
        <Text type="secondary">{t('report.footer')}</Text>
      </div>
    </div>
  );
};

export default ProjectReportContent;
